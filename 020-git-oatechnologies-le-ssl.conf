<IfModule mod_ssl.c>
<VirtualHost *:443>
    ServerName git.oatechnologies.us
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/git_oatechnologies

    # GitLab reverse proxy configuration
    ProxyPreserveHost On
    ProxyRequests Off

    # Main GitLab proxy
    ProxyPass / http://127.0.0.1:8080/
    ProxyPassReverse / http://127.0.0.1:8080/

    # WebSocket support for GitLab
    RewriteEngine On
    RewriteCond %{HTTP:Upgrade} websocket [NC]
    RewriteCond %{HTTP:Connection} upgrade [NC]
    RewriteRule ^/?(.*) "ws://127.0.0.1:8080/$1" [P,L]

    # Headers for proper GitLab functionality
    RequestHeader set X-Forwarded-Proto "https"
    RequestHeader set X-Forwarded-Ssl on

    <Directory /var/www/git_oatechnologies/>
        Require all granted
        AllowOverride All
        Options FollowSymLinks MultiViews
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/git_oatechnologies_error.log
    CustomLog ${APACHE_LOG_DIR}/git_oatechnologies_access.log combined

    # Redirect HTTP to HTTPS
    RewriteEngine on
# Some rewrite rules in this file were disabled on your HTTPS site,
# because they have the potential to create redirection loops.

#     RewriteCond %{SERVER_NAME} =git.oatechnologies.us
#     RewriteRule ^ https://%{SERVER_NAME}%{REQUEST_URI} [END,NE,R=permanent]


SSLCertificateFile /etc/letsencrypt/live/git.oatechnologies.us/fullchain.pem
SSLCertificateKeyFile /etc/letsencrypt/live/git.oatechnologies.us/privkey.pem
Include /etc/letsencrypt/options-ssl-apache.conf
</VirtualHost>
</IfModule>
